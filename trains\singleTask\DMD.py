"""
DMD (Decoupled Multimodal Distilling) 训练器
用于多模态情感识别的解耦多模态蒸馏方法的训练和测试

该文件实现了DMD模型的训练流程，包括：
1. 多种损失函数的计算（任务损失、重构损失、正交损失、相似度损失、图蒸馏损失）
2. 训练循环和验证流程
3. 模型保存和早停机制
"""

import logging
import numpy as np
import torch
import torch.nn as nn
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from tqdm import tqdm
from ..utils import MetricsTop, dict_to_str
from .HingeLoss import HingeLoss

logger = logging.getLogger('MMSA')

class MSE(nn.Module):
    """
    自定义均方误差损失函数
    用于计算重构损失和循环一致性损失
    """
    def __init__(self):
        super(MSE, self).__init__()

    def forward(self, pred, real):
        """
        计算预测值和真实值之间的均方误差

        Args:
            pred: 预测值张量
            real: 真实值张量

        Returns:
            mse: 均方误差值
        """
        diffs = torch.add(real, -pred)  # 计算差值
        n = torch.numel(diffs.data)     # 获取元素总数
        mse = torch.sum(diffs.pow(2)) / n  # 计算均方误差
        return mse

class DMD():
    """
    DMD模型的训练器类

    负责DMD模型的训练、验证和测试流程
    包含多种损失函数的计算和优化策略
    """
    def __init__(self, args):
        """
        初始化DMD训练器

        Args:
            args: 包含训练参数的配置对象
        """
        self.args = args
        # 主要任务损失函数（L1损失，对回归任务更鲁棒）
        self.criterion = nn.L1Loss()
        # 余弦嵌入损失，用于计算特征间的正交性
        self.cosine = nn.CosineEmbeddingLoss()
        # 评估指标计算器
        self.metrics = MetricsTop(args.train_mode).getMetics(args.dataset_name)
        # 自定义MSE损失，用于重构损失
        self.MSE = MSE()
        # Hinge损失，用于相似度学习
        self.sim_loss = HingeLoss()

    def do_train(self, model, dataloader, return_epoch_results=False):
        """
        执行DMD模型的训练过程

        Args:
            model: 包含三个模型的列表 [DMD主模型, 同构图蒸馏模型, 异构图蒸馏模型]
            dataloader: 数据加载器字典，包含train/valid/test数据
            return_epoch_results: 是否返回每个epoch的详细结果

        Returns:
            epoch_results: 如果return_epoch_results=True，返回训练过程中的详细结果
        """

        # ===== 模型和优化器初始化 =====
        # model[0]: DMD主模型, model[1]: 同构图蒸馏模型, model[2]: 异构图蒸馏模型
        params = list(model[0].parameters()) + \
                 list(model[1].parameters()) + \
                 list(model[2].parameters())

        # 使用Adam优化器
        optimizer = optim.Adam(params, lr=self.args.learning_rate)
        # 学习率调度器：当验证损失不再下降时减少学习率
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, verbose=True, patience=self.args.patience)

        # ===== 训练状态初始化 =====
        epochs, best_epoch = 0, 0  # 当前epoch数和最佳epoch数
        if return_epoch_results:
            # 存储每个epoch的训练结果
            epoch_results = {
                'train': [],
                'valid': [],
                'test': []
            }
        # 确定评估指标的优化方向（最小化损失 vs 最大化准确率）
        min_or_max = 'min' if self.args.KeyEval in ['Loss'] else 'max'
        best_valid = 1e8 if min_or_max == 'min' else 0  # 初始化最佳验证结果

        # ===== 模型组织 =====
        net = []
        net_dmd = model[0]              # DMD主模型
        net_distill_homo = model[1]     # 同构图蒸馏模型
        net_distill_hetero = model[2]   # 异构图蒸馏模型
        net.append(net_dmd)
        net.append(net_distill_homo)
        net.append(net_distill_hetero)
        model = net

        # ===== 主训练循环 =====
        while True:
            epochs += 1
            y_pred, y_true = [], []  # 存储预测值和真实值用于评估

            # 设置所有模型为训练模式
            for mod in model:
                mod.train()

            train_loss = 0.0
            left_epochs = self.args.update_epochs  # 梯度累积的剩余步数

            # 使用tqdm显示训练进度
            with tqdm(dataloader['train']) as td:
                for batch_data in td:

                    # ===== 梯度累积策略 =====
                    # 每update_epochs个batch才进行一次梯度清零
                    if left_epochs == self.args.update_epochs:
                        optimizer.zero_grad()
                    left_epochs -= 1

                    # ===== 数据准备 =====
                    # 将数据移动到GPU
                    vision = batch_data['vision'].to(self.args.device)  # 视频特征
                    audio = batch_data['audio'].to(self.args.device)    # 音频特征
                    text = batch_data['text'].to(self.args.device)      # 文本特征
                    labels = batch_data['labels']['M'].to(self.args.device)  # 情感标签
                    labels = labels.view(-1, 1)  # 调整标签形状

                    # ===== 前向传播 =====
                    # 初始化图蒸馏所需的logits和表示
                    logits_homo, reprs_homo, logits_hetero, reprs_hetero = [], [], [], []

                    # DMD主模型前向传播，获取所有中间结果
                    output = model[0](text, audio, vision, is_distill=True)

                    # ===== 收集同构图蒸馏数据 =====
                    # 同构图蒸馏使用模态不变特征的预测结果
                    logits_homo.append(output['logits_l_homo'])  # 文本模态的同构图预测
                    logits_homo.append(output['logits_v_homo'])  # 视频模态的同构图预测
                    logits_homo.append(output['logits_a_homo'])  # 音频模态的同构图预测

                    # 同构图蒸馏使用的特征表示
                    reprs_homo.append(output['repr_l_homo'])     # 文本模态的同构图表示
                    reprs_homo.append(output['repr_v_homo'])     # 视频模态的同构图表示
                    reprs_homo.append(output['repr_a_homo'])     # 音频模态的同构图表示

                    # ===== 收集异构图蒸馏数据 =====
                    # 异构图蒸馏使用跨模态融合后的预测结果
                    logits_hetero.append(output['logits_l_hetero'])  # 文本模态的异构图预测
                    logits_hetero.append(output['logits_v_hetero'])  # 视频模态的异构图预测
                    logits_hetero.append(output['logits_a_hetero'])  # 音频模态的异构图预测

                    # 异构图蒸馏使用的特征表示
                    reprs_hetero.append(output['repr_l_hetero'])     # 文本模态的异构图表示
                    reprs_hetero.append(output['repr_v_hetero'])     # 视频模态的异构图表示
                    reprs_hetero.append(output['repr_a_hetero'])     # 音频模态的异构图表示

                    # ===== 准备图蒸馏输入 =====
                    # 将列表转换为张量，形状为 [n_modalities, batch_size, feature_dim]
                    logits_homo = torch.stack(logits_homo)      # 同构图的预测logits
                    reprs_homo = torch.stack(reprs_homo)        # 同构图的特征表示

                    logits_hetero = torch.stack(logits_hetero)  # 异构图的预测logits
                    reprs_hetero = torch.stack(reprs_hetero)    # 异构图的特征表示

                    # ===== 图蒸馏边权重计算 =====
                    # 同构图蒸馏：计算模态不变特征间的蒸馏权重
                    edges_homo, edges_origin_homo = model[1](logits_homo, reprs_homo)

                    # 异构图蒸馏：计算跨模态融合特征间的蒸馏权重
                    edges_hetero, edges_origin_hetero = model[2](logits_hetero, reprs_hetero)

                    # ===== 任务损失计算 =====
                    # 主要任务损失：最终融合结果的预测损失
                    loss_task_all = self.criterion(output['output_logit'], labels)

                    # 同构图蒸馏的任务损失（基于模态不变特征）
                    loss_task_l_homo = self.criterion(output['logits_l_homo'], labels)    # 文本同构图损失
                    loss_task_v_homo = self.criterion(output['logits_v_homo'], labels)    # 视频同构图损失
                    loss_task_a_homo = self.criterion(output['logits_a_homo'], labels)    # 音频同构图损失

                    # 异构图蒸馏的任务损失（基于跨模态融合特征）
                    loss_task_l_hetero = self.criterion(output['logits_l_hetero'], labels)  # 文本异构图损失
                    loss_task_v_hetero = self.criterion(output['logits_v_hetero'], labels)  # 视频异构图损失
                    loss_task_a_hetero = self.criterion(output['logits_a_hetero'], labels)  # 音频异构图损失

                    # 模态不变特征融合的任务损失
                    loss_task_c = self.criterion(output['logits_c'], labels)

                    # 总任务损失：所有预测头的损失之和
                    loss_task = loss_task_all + loss_task_l_homo + loss_task_v_homo + loss_task_a_homo + \
                               loss_task_l_hetero + loss_task_v_hetero + loss_task_a_hetero + loss_task_c

                    # reconstruction loss
                    loss_recon_l = self.MSE(output['recon_l'], output['origin_l'])
                    loss_recon_v = self.MSE(output['recon_v'], output['origin_v'])
                    loss_recon_a = self.MSE(output['recon_a'], output['origin_a'])
                    loss_recon = loss_recon_l + loss_recon_v + loss_recon_a

                    # cycle consistency loss between s_x and s_x_r
                    loss_sl_slr = self.MSE(output['s_l'].permute(1, 2, 0), output['s_l_r'])
                    loss_sv_slv = self.MSE(output['s_v'].permute(1, 2, 0), output['s_v_r'])
                    loss_sa_sla = self.MSE(output['s_a'].permute(1, 2, 0), output['s_a_r'])
                    loss_s_sr = loss_sl_slr + loss_sv_slv + loss_sa_sla

                    # ort loss
                    cosine_similarity_s_c_l = self.cosine(output['s_l'], output['c_l'],
                                                          torch.tensor([-1]).cuda()).mean(0)
                    cosine_similarity_s_c_v = self.cosine(output['s_v'], output['c_v'],
                                                          torch.tensor([-1]).cuda()).mean(0)
                    cosine_similarity_s_c_a = self.cosine(output['s_a'], output['c_a'],
                                                          torch.tensor([-1]).cuda()).mean(0)
                    loss_ort = cosine_similarity_s_c_l + cosine_similarity_s_c_v + cosine_similarity_s_c_a

                    # margin loss
                    c_l, c_v, c_a = output['c_l_sim'], output['c_v_sim'], output['c_a_sim']
                    ids, feats = [], []
                    for i in range(labels.size(0)):
                        feats.append(c_l[i].view(1, -1))
                        feats.append(c_v[i].view(1, -1))
                        feats.append(c_a[i].view(1, -1))
                        ids.append(labels[i].view(1, -1))
                        ids.append(labels[i].view(1, -1))
                        ids.append(labels[i].view(1, -1))
                    feats = torch.cat(feats, dim=0)
                    ids = torch.cat(ids, dim=0)
                    loss_sim = self.sim_loss(ids, feats)

                    # homo GD loss
                    loss_reg_homo, loss_logit_homo, loss_repr_homo = \
                        model[1].distillation_loss(logits_homo, reprs_homo, edges_homo)
                    graph_distill_loss_homo = 0.05 * (loss_logit_homo + loss_reg_homo)

                    # hetero GD loss
                    loss_reg_hetero, loss_logit_hetero, loss_repr_hetero = \
                        model[2].distillation_loss(logits_hetero, reprs_hetero, edges_hetero)
                    graph_distill_loss_hetero = 0.05 * (loss_logit_hetero + loss_repr_hetero + loss_reg_hetero)

                    combined_loss = loss_task + \
                                    graph_distill_loss_homo + graph_distill_loss_hetero + \
                                    (loss_s_sr + loss_recon + (loss_sim+loss_ort) * 0.1) * 0.1

                    combined_loss.backward()


                    if self.args.grad_clip != -1.0:
                        params = list(model[0].parameters()) + \
                                 list(model[1].parameters()) + \
                                 list(model[2].parameters())
                        nn.utils.clip_grad_value_(params, self.args.grad_clip)

                    train_loss += combined_loss.item()

                    y_pred.append(output['output_logit'].cpu())
                    y_true.append(labels.cpu())
                    if not left_epochs:
                        optimizer.step()
                        left_epochs = self.args.update_epochs
                if not left_epochs:
                    # update
                    optimizer.step()

            train_loss = train_loss / len(dataloader['train'])
            pred, true = torch.cat(y_pred), torch.cat(y_true)
            train_results = self.metrics(pred, true)
            logger.info(
                f">> Epoch: {epochs} "
                f"TRAIN-({self.args.model_name}) [{epochs - best_epoch}/{epochs}/{self.args.cur_seed}] "
                f">> total_loss: {round(train_loss, 4)} "
                f"{dict_to_str(train_results)}"
            )
            # validation
            val_results = self.do_test(model[0], dataloader['valid'], mode="VAL")
            test_results = self.do_test(model[0], dataloader['test'], mode="TEST")
            cur_valid = val_results[self.args.KeyEval]
            scheduler.step(val_results['Loss'])
            # save each epoch model
            torch.save(model[0].state_dict(), './pt/' + str(epochs) + '.pth')
            # save best model
            isBetter = cur_valid <= (best_valid - 1e-6) if min_or_max == 'min' else cur_valid >= (best_valid + 1e-6)
            if isBetter:
                best_valid, best_epoch = cur_valid, epochs
                # save model
                model_save_path = './pt/dmd.pth'
                torch.save(model[0].state_dict(), model_save_path)

            if return_epoch_results:
                train_results["Loss"] = train_loss
                epoch_results['train'].append(train_results)
                epoch_results['valid'].append(val_results)
                test_results = self.do_test(model, dataloader['test'], mode="TEST")
                epoch_results['test'].append(test_results)
            # early stop
            if epochs - best_epoch >= self.args.early_stop:
                return epoch_results if return_epoch_results else None

    def do_test(self, model, dataloader, mode="VAL", return_sample_results=False):

        model.eval()
        y_pred, y_true = [], []

        eval_loss = 0.0
        if return_sample_results:
            ids, sample_results = [], []
            all_labels = []
            features = {
                "Feature_t": [],
                "Feature_a": [],
                "Feature_v": [],
                "Feature_f": [],
            }

        with torch.no_grad():
            with tqdm(dataloader) as td:
                for batch_data in td:
                    vision = batch_data['vision'].to(self.args.device)
                    audio = batch_data['audio'].to(self.args.device)
                    text = batch_data['text'].to(self.args.device)
                    labels = batch_data['labels']['M'].to(self.args.device)
                    labels = labels.view(-1, 1)
                    output = model(text, audio, vision, is_distill=True)
                    loss = self.criterion(output['output_logit'], labels)
                    eval_loss += loss.item()
                    y_pred.append(output['output_logit'].cpu())
                    y_true.append(labels.cpu())

        eval_loss = eval_loss / len(dataloader)
        pred, true = torch.cat(y_pred), torch.cat(y_true)

        eval_results = self.metrics(pred, true)
        eval_results["Loss"] = round(eval_loss, 4)
        logger.info(f"{mode}-({self.args.model_name}) >> {dict_to_str(eval_results)}")

        if return_sample_results:
            eval_results["Ids"] = ids
            eval_results["SResults"] = sample_results
            for k in features.keys():
                features[k] = np.concatenate(features[k], axis=0)
            eval_results['Features'] = features
            eval_results['Labels'] = all_labels

        return eval_results