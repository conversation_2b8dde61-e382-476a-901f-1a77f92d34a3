"""
DMD (Decoupled Multimodal Distilling) 训练器
用于多模态情感识别的解耦多模态蒸馏方法的训练和测试

该文件实现了DMD模型的训练流程，包括：
1. 多种损失函数的计算（任务损失、重构损失、正交损失、相似度损失、图蒸馏损失）
2. 训练循环和验证流程
3. 模型保存和早停机制
"""

import logging
import numpy as np
import torch
import torch.nn as nn
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from tqdm import tqdm
from ..utils import MetricsTop, dict_to_str
from .HingeLoss import HingeLoss

logger = logging.getLogger('MMSA')

class MSE(nn.Module):
    """
    自定义均方误差损失函数
    用于计算重构损失和循环一致性损失
    """
    def __init__(self):
        super(MSE, self).__init__()

    def forward(self, pred, real):
        """
        计算预测值和真实值之间的均方误差

        Args:
            pred: 预测值张量
            real: 真实值张量

        Returns:
            mse: 均方误差值
        """
        diffs = torch.add(real, -pred)  # 计算差值
        n = torch.numel(diffs.data)     # 获取元素总数
        mse = torch.sum(diffs.pow(2)) / n  # 计算均方误差
        return mse

class DMD():
    """
    DMD模型的训练器类

    负责DMD模型的训练、验证和测试流程
    包含多种损失函数的计算和优化策略
    """
    def __init__(self, args):
        """
        初始化DMD训练器

        Args:
            args: 包含训练参数的配置对象
        """
        self.args = args
        # 主要任务损失函数（L1损失，对回归任务更鲁棒）
        self.criterion = nn.L1Loss()
        # 余弦嵌入损失，用于计算特征间的正交性
        self.cosine = nn.CosineEmbeddingLoss()
        # 评估指标计算器
        self.metrics = MetricsTop(args.train_mode).getMetics(args.dataset_name)
        # 自定义MSE损失，用于重构损失
        self.MSE = MSE()
        # Hinge损失，用于相似度学习
        self.sim_loss = HingeLoss()

    def do_train(self, model, dataloader, return_epoch_results=False):
        """
        执行DMD模型的训练过程

        Args:
            model: 包含三个模型的列表 [DMD主模型, 同构图蒸馏模型, 异构图蒸馏模型]
            dataloader: 数据加载器字典，包含train/valid/test数据
            return_epoch_results: 是否返回每个epoch的详细结果

        Returns:
            epoch_results: 如果return_epoch_results=True，返回训练过程中的详细结果
        """

        # ===== 模型和优化器初始化 =====
        # model[0]: DMD主模型, model[1]: 同构图蒸馏模型, model[2]: 异构图蒸馏模型
        params = list(model[0].parameters()) + \
                 list(model[1].parameters()) + \
                 list(model[2].parameters())

        # 使用Adam优化器
        optimizer = optim.Adam(params, lr=self.args.learning_rate)
        # 学习率调度器：当验证损失不再下降时减少学习率
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, verbose=True, patience=self.args.patience)

        # ===== 训练状态初始化 =====
        epochs, best_epoch = 0, 0  # 当前epoch数和最佳epoch数
        if return_epoch_results:
            # 存储每个epoch的训练结果
            epoch_results = {
                'train': [],
                'valid': [],
                'test': []
            }
        # 确定评估指标的优化方向（最小化损失 vs 最大化准确率）
        min_or_max = 'min' if self.args.KeyEval in ['Loss'] else 'max'
        best_valid = 1e8 if min_or_max == 'min' else 0  # 初始化最佳验证结果

        # ===== 模型组织 =====
        net = []
        net_dmd = model[0]              # DMD主模型
        net_distill_homo = model[1]     # 同构图蒸馏模型
        net_distill_hetero = model[2]   # 异构图蒸馏模型
        net.append(net_dmd)
        net.append(net_distill_homo)
        net.append(net_distill_hetero)
        model = net

        # ===== 主训练循环 =====
        while True:
            epochs += 1
            y_pred, y_true = [], []  # 存储预测值和真实值用于评估

            # 设置所有模型为训练模式
            for mod in model:
                mod.train()

            train_loss = 0.0
            left_epochs = self.args.update_epochs  # 梯度累积的剩余步数

            # 使用tqdm显示训练进度
            with tqdm(dataloader['train']) as td:
                for batch_data in td:

                    # ===== 梯度累积策略 =====
                    # 每update_epochs个batch才进行一次梯度清零
                    if left_epochs == self.args.update_epochs:
                        optimizer.zero_grad()
                    left_epochs -= 1

                    # ===== 数据准备 =====
                    # 将数据移动到GPU
                    vision = batch_data['vision'].to(self.args.device)  # 视频特征
                    audio = batch_data['audio'].to(self.args.device)    # 音频特征
                    text = batch_data['text'].to(self.args.device)      # 文本特征
                    labels = batch_data['labels']['M'].to(self.args.device)  # 情感标签
                    labels = labels.view(-1, 1)  # 调整标签形状

                    # ===== 前向传播 =====
                    # 初始化图蒸馏所需的logits和表示++++++++++++++++++++++++
                    logits_homo, reprs_homo, logits_hetero, reprs_hetero = [], [], [], []

                    # DMD主模型前向传播，获取所有中间结果
                    output = model[0](text, audio, vision, is_distill=True)

                    # ===== 收集同构图蒸馏数据 =====
                    # 同构图蒸馏使用模态不变特征的预测结果
                    logits_homo.append(output['logits_l_homo'])  # 文本模态的同构图预测
                    logits_homo.append(output['logits_v_homo'])  # 视频模态的同构图预测
                    logits_homo.append(output['logits_a_homo'])  # 音频模态的同构图预测

                    # 同构图蒸馏使用的特征表示
                    reprs_homo.append(output['repr_l_homo'])     # 文本模态的同构图表示
                    reprs_homo.append(output['repr_v_homo'])     # 视频模态的同构图表示
                    reprs_homo.append(output['repr_a_homo'])     # 音频模态的同构图表示

                    # ===== 收集异构图蒸馏数据 =====
                    # 异构图蒸馏使用跨模态融合后的预测结果
                    logits_hetero.append(output['logits_l_hetero'])  # 文本模态的异构图预测
                    logits_hetero.append(output['logits_v_hetero'])  # 视频模态的异构图预测
                    logits_hetero.append(output['logits_a_hetero'])  # 音频模态的异构图预测

                    # 异构图蒸馏使用的特征表示
                    reprs_hetero.append(output['repr_l_hetero'])     # 文本模态的异构图表示
                    reprs_hetero.append(output['repr_v_hetero'])     # 视频模态的异构图表示
                    reprs_hetero.append(output['repr_a_hetero'])     # 音频模态的异构图表示

                    # ===== 准备图蒸馏输入 =====
                    # 将列表转换为张量，形状为 [n_modalities, batch_size, feature_dim]
                    logits_homo = torch.stack(logits_homo)      # 同构图的预测logits
                    reprs_homo = torch.stack(reprs_homo)        # 同构图的特征表示

                    logits_hetero = torch.stack(logits_hetero)  # 异构图的预测logits
                    reprs_hetero = torch.stack(reprs_hetero)    # 异构图的特征表示

                    # ===== 图蒸馏边权重计算 =====
                    # 同构图蒸馏：计算模态不变特征间的蒸馏权重
                    edges_homo, edges_origin_homo = model[1](logits_homo, reprs_homo)

                    # 异构图蒸馏：计算跨模态融合特征间的蒸馏权重
                    edges_hetero, edges_origin_hetero = model[2](logits_hetero, reprs_hetero)

                    # ===== 任务损失计算 =====
                    # 主要任务损失：最终融合结果的预测损失
                    loss_task_all = self.criterion(output['output_logit'], labels)

                    # 同构图蒸馏的任务损失（基于模态不变特征）
                    loss_task_l_homo = self.criterion(output['logits_l_homo'], labels)    # 文本同构图损失
                    loss_task_v_homo = self.criterion(output['logits_v_homo'], labels)    # 视频同构图损失
                    loss_task_a_homo = self.criterion(output['logits_a_homo'], labels)    # 音频同构图损失

                    # 异构图蒸馏的任务损失（基于跨模态融合特征）
                    loss_task_l_hetero = self.criterion(output['logits_l_hetero'], labels)  # 文本异构图损失
                    loss_task_v_hetero = self.criterion(output['logits_v_hetero'], labels)  # 视频异构图损失
                    loss_task_a_hetero = self.criterion(output['logits_a_hetero'], labels)  # 音频异构图损失

                    # 模态不变特征融合的任务损失
                    loss_task_c = self.criterion(output['logits_c'], labels)

                    # 总任务损失：所有预测头的损失之和
                    loss_task = loss_task_all + loss_task_l_homo + loss_task_v_homo + loss_task_a_homo + \
                               loss_task_l_hetero + loss_task_v_hetero + loss_task_a_hetero + loss_task_c

                    # ===== 重构损失计算 =====
                    # 确保解耦后的特征能够重构原始特征，保证信息不丢失
                    loss_recon_l = self.MSE(output['recon_l'], output['origin_l'])  # 文本重构损失
                    loss_recon_v = self.MSE(output['recon_v'], output['origin_v'])  # 视频重构损失
                    loss_recon_a = self.MSE(output['recon_a'], output['origin_a'])  # 音频重构损失
                    loss_recon = loss_recon_l + loss_recon_v + loss_recon_a        # 总重构损失

                    # ===== 循环一致性损失 =====
                    # 确保模态特定特征在重构过程中保持一致性
                    # s_x: 原始模态特定特征, s_x_r: 重构后的模态特定特征
                    loss_sl_slr = self.MSE(output['s_l'].permute(1, 2, 0), output['s_l_r'])  # 文本模态特定特征一致性
                    loss_sv_slv = self.MSE(output['s_v'].permute(1, 2, 0), output['s_v_r'])  # 视频模态特定特征一致性
                    loss_sa_sla = self.MSE(output['s_a'].permute(1, 2, 0), output['s_a_r'])  # 音频模态特定特征一致性
                    loss_s_sr = loss_sl_slr + loss_sv_slv + loss_sa_sla                      # 总循环一致性损失

                    # ===== 正交损失（Orthogonal Loss） =====
                    # 确保模态特定特征和模态不变特征相互正交（不相关）
                    # 使用余弦相似度损失，目标是-1（完全负相关/正交）
                    cosine_similarity_s_c_l = self.cosine(output['s_l'], output['c_l'],
                                                          torch.tensor([-1]).cuda()).mean(0)  # 文本模态正交性
                    cosine_similarity_s_c_v = self.cosine(output['s_v'], output['c_v'],
                                                          torch.tensor([-1]).cuda()).mean(0)  # 视频模态正交性
                    cosine_similarity_s_c_a = self.cosine(output['s_a'], output['c_a'],
                                                          torch.tensor([-1]).cuda()).mean(0)  # 音频模态正交性
                    loss_ort = cosine_similarity_s_c_l + cosine_similarity_s_c_v + cosine_similarity_s_c_a

                    # ===== 相似度损失（Margin Loss） =====
                    # 确保相同情感标签的模态不变特征在不同模态间相似
                    # 使用对齐后的模态不变特征进行相似度学习
                    c_l, c_v, c_a = output['c_l_sim'], output['c_v_sim'], output['c_a_sim']
                    ids, feats = [], []
                    # 为每个样本构建三元组：(文本特征, 视频特征, 音频特征) 对应同一个标签
                    for i in range(labels.size(0)):
                        feats.append(c_l[i].view(1, -1))  # 文本模态不变特征
                        feats.append(c_v[i].view(1, -1))  # 视频模态不变特征
                        feats.append(c_a[i].view(1, -1))  # 音频模态不变特征
                        ids.append(labels[i].view(1, -1))  # 对应的情感标签
                        ids.append(labels[i].view(1, -1))  # 对应的情感标签
                        ids.append(labels[i].view(1, -1))  # 对应的情感标签
                    feats = torch.cat(feats, dim=0)  # 拼接所有特征
                    ids = torch.cat(ids, dim=0)      # 拼接所有标签
                    loss_sim = self.sim_loss(ids, feats)  # 计算Hinge损失

                    # ===== 同构图蒸馏损失 =====
                    # 计算同构图蒸馏的三种损失：正则化损失、logit损失、表示损失
                    loss_reg_homo, loss_logit_homo, loss_repr_homo = \
                        model[1].distillation_loss(logits_homo, reprs_homo, edges_homo)
                    # 同构图蒸馏总损失（权重0.05）
                    graph_distill_loss_homo = 0.05 * (loss_logit_homo + loss_reg_homo)

                    # ===== 异构图蒸馏损失 =====
                    # 计算异构图蒸馏的三种损失：正则化损失、logit损失、表示损失
                    loss_reg_hetero, loss_logit_hetero, loss_repr_hetero = \
                        model[2].distillation_loss(logits_hetero, reprs_hetero, edges_hetero)
                    # 异构图蒸馏总损失（权重0.05）
                    graph_distill_loss_hetero = 0.05 * (loss_logit_hetero + loss_repr_hetero + loss_reg_hetero)

                    # ===== 总损失计算 =====
                    # 组合所有损失项：
                    # 1. 任务损失（主要）
                    # 2. 图蒸馏损失（同构+异构）
                    # 3. 辅助损失（重构+循环一致性+相似度+正交性，权重0.1）
                    combined_loss = loss_task + \
                                    graph_distill_loss_homo + graph_distill_loss_hetero + \
                                    (loss_s_sr + loss_recon + (loss_sim+loss_ort) * 0.1) * 0.1

                    # ===== 反向传播 =====
                    combined_loss.backward()

                    # ===== 梯度裁剪 =====
                    # 防止梯度爆炸，提高训练稳定性
                    if self.args.grad_clip != -1.0:
                        params = list(model[0].parameters()) + \
                                 list(model[1].parameters()) + \
                                 list(model[2].parameters())
                        nn.utils.clip_grad_value_(params, self.args.grad_clip)

                    # ===== 累积损失和预测结果 =====
                    train_loss += combined_loss.item()
                    y_pred.append(output['output_logit'].cpu())  # 保存预测结果用于评估
                    y_true.append(labels.cpu())                  # 保存真实标签用于评估

                    # ===== 梯度累积和优化器更新 =====
                    if not left_epochs:
                        optimizer.step()  # 执行参数更新
                        left_epochs = self.args.update_epochs  # 重置累积计数器

                # 处理最后一个不完整的batch
                if not left_epochs:
                    optimizer.step()  # 确保最后的梯度也被更新

            # ===== Epoch结束后的评估和保存 =====
            # 计算平均训练损失
            train_loss = train_loss / len(dataloader['train'])
            # 拼接所有batch的预测结果和真实标签
            pred, true = torch.cat(y_pred), torch.cat(y_true)
            # 计算训练指标（准确率、F1等）
            train_results = self.metrics(pred, true)
            # 记录训练日志
            logger.info(
                f">> Epoch: {epochs} "
                f"TRAIN-({self.args.model_name}) [{epochs - best_epoch}/{epochs}/{self.args.cur_seed}] "
                f">> total_loss: {round(train_loss, 4)} "
                f"{dict_to_str(train_results)}"
            )

            # ===== 验证和测试 =====
            # 在验证集上评估模型性能
            val_results = self.do_test(model[0], dataloader['valid'], mode="VAL")
            # 在测试集上评估模型性能
            test_results = self.do_test(model[0], dataloader['test'], mode="TEST")
            # 获取当前验证集上的关键评估指标
            cur_valid = val_results[self.args.KeyEval]
            # 根据验证损失调整学习率
            scheduler.step(val_results['Loss'])

            # ===== 模型保存策略 =====
            # 保存每个epoch的模型（用于调试和分析）
            torch.save(model[0].state_dict(), './pt/' + str(epochs) + '.pth')

            # 判断当前模型是否比历史最佳模型更好
            isBetter = cur_valid <= (best_valid - 1e-6) if min_or_max == 'min' else cur_valid >= (best_valid + 1e-6)
            if isBetter:
                best_valid, best_epoch = cur_valid, epochs  # 更新最佳结果和epoch
                # 保存最佳模型
                model_save_path = './pt/dmd.pth'
                torch.save(model[0].state_dict(), model_save_path)

            # ===== 结果记录 =====
            if return_epoch_results:
                train_results["Loss"] = train_loss
                epoch_results['train'].append(train_results)    # 记录训练结果
                epoch_results['valid'].append(val_results)      # 记录验证结果
                test_results = self.do_test(model, dataloader['test'], mode="TEST")
                epoch_results['test'].append(test_results)      # 记录测试结果

            # ===== 早停机制 =====
            # 如果验证性能在early_stop个epoch内没有改善，则停止训练
            if epochs - best_epoch >= self.args.early_stop:
                return epoch_results if return_epoch_results else None

    def do_test(self, model, dataloader, mode="VAL", return_sample_results=False):
        """
        执行模型测试/验证

        Args:
            model: 要测试的DMD模型
            dataloader: 测试数据加载器
            mode: 测试模式 ("VAL"验证, "TEST"测试)
            return_sample_results: 是否返回样本级别的详细结果

        Returns:
            eval_results: 包含评估指标的字典
        """

        # ===== 设置模型为评估模式 =====
        model.eval()  # 关闭dropout和batch normalization的训练模式
        y_pred, y_true = [], []  # 存储预测值和真实值

        eval_loss = 0.0  # 累积评估损失

        # ===== 样本级结果记录（可选） =====
        if return_sample_results:
            ids, sample_results = [], []  # 样本ID和详细结果
            all_labels = []               # 所有标签
            features = {                  # 特征存储
                "Feature_t": [],          # 文本特征
                "Feature_a": [],          # 音频特征
                "Feature_v": [],          # 视频特征
                "Feature_f": [],          # 融合特征
            }

        # ===== 测试循环（无梯度计算） =====
        with torch.no_grad():  # 禁用梯度计算，节省内存和计算
            with tqdm(dataloader) as td:  # 显示测试进度
                for batch_data in td:
                    # ===== 数据准备 =====
                    vision = batch_data['vision'].to(self.args.device)  # 视频特征
                    audio = batch_data['audio'].to(self.args.device)    # 音频特征
                    text = batch_data['text'].to(self.args.device)      # 文本特征
                    labels = batch_data['labels']['M'].to(self.args.device)  # 情感标签
                    labels = labels.view(-1, 1)  # 调整标签形状

                    # ===== 前向传播 =====
                    output = model(text, audio, vision, is_distill=True)

                    # ===== 损失计算 =====
                    loss = self.criterion(output['output_logit'], labels)
                    eval_loss += loss.item()  # 累积损失

                    # ===== 结果收集 =====
                    y_pred.append(output['output_logit'].cpu())  # 预测结果移到CPU
                    y_true.append(labels.cpu())                  # 真实标签移到CPU

        # ===== 结果统计和评估 =====
        eval_loss = eval_loss / len(dataloader)  # 计算平均损失
        pred, true = torch.cat(y_pred), torch.cat(y_true)  # 拼接所有batch的结果

        # 计算评估指标（准确率、F1分数、MAE等）
        eval_results = self.metrics(pred, true)
        eval_results["Loss"] = round(eval_loss, 4)  # 添加损失到结果中

        # 记录评估日志
        logger.info(f"{mode}-({self.args.model_name}) >> {dict_to_str(eval_results)}")

        # ===== 样本级结果处理（可选） =====
        if return_sample_results:
            eval_results["Ids"] = ids                    # 样本ID
            eval_results["SResults"] = sample_results    # 样本级结果
            # 拼接所有特征
            for k in features.keys():
                features[k] = np.concatenate(features[k], axis=0)
            eval_results['Features'] = features          # 特征
            eval_results['Labels'] = all_labels          # 标签

        return eval_results