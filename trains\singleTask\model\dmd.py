"""
DMD (Dynamic Multimodal Decoupling) 模型的主要骨干网络
包含特征解耦和多模态变换器的实现

该模型用于多模态情感分析，能够处理文本(Language)、音频(Audio)、视频(Video)三种模态的数据
通过特征解耦将每种模态分解为模态特定特征和模态不变特征，然后进行跨模态融合
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from ...subNets import BertTextEncoder
from ...subNets.transformers_encoder.transformer import TransformerEncoder

class DMD(nn.Module):
    """
    DMD (Dynamic Multimodal Decoupling) 模型

    该模型的核心思想是将多模态特征解耦为：
    1. 模态特定特征 (Modality-Specific Features): 每种模态独有的特征
    2. 模态不变特征 (Modality-Invariant Features): 跨模态共享的特征

    然后通过自注意力和跨模态注意力机制进行特征融合，最终输出情感预测结果
    """
    def __init__(self, args):
        super(DMD, self).__init__()

        # 文本编码器初始化 - 如果使用BERT则加载预训练的BERT模型
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert

        # 获取目标特征维度和注意力头数
        dst_feature_dims, nheads = args.dst_feature_dim_nheads

        # 根据数据集设置序列长度
        # MOSI数据集的序列长度设置
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:  # 数据对齐情况下，三种模态长度相同
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:  # 数据未对齐情况下，保持原始长度
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        # MOSEI数据集的序列长度设置
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500

        # 特征维度设置
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims  # 原始特征维度
        self.d_l = self.d_a = self.d_v = dst_feature_dims  # 目标特征维度（统一维度）

        # Transformer相关参数
        self.num_heads = nheads  # 注意力头数
        self.layers = args.nlevels  # Transformer层数

        # Dropout参数设置
        self.attn_dropout = args.attn_dropout  # 注意力dropout
        self.attn_dropout_a = args.attn_dropout_a  # 音频注意力dropout
        self.attn_dropout_v = args.attn_dropout_v  # 视频注意力dropout
        self.relu_dropout = args.relu_dropout  # ReLU dropout
        self.embed_dropout = args.embed_dropout  # 嵌入dropout
        self.res_dropout = args.res_dropout  # 残差连接dropout
        self.output_dropout = args.output_dropout  # 输出dropout
        self.text_dropout = args.text_dropout  # 文本dropout
        self.attn_mask = args.attn_mask  # 注意力掩码

        # 组合特征维度计算
        combined_dim_low = self.d_a  # 低级特征组合维度
        combined_dim_high = 2 * self.d_a  # 高级特征组合维度
        combined_dim = 2 * (self.d_l + self.d_a + self.d_v) + self.d_l * 3  # 最终组合维度
        output_dim = 1  # 输出维度（情感分析为1维）

        # ===== 1. 时序卷积层：用于初始特征投影 =====
        # 将原始特征维度投影到统一的目标维度，便于后续处理
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)  # 文本特征投影
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)  # 音频特征投影
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)  # 视频特征投影

        # ===== 2.1 模态特定编码器 (Modality-Specific Encoder) =====
        # 用于提取每种模态独有的特征，这些特征不能被其他模态表示
        self.encoder_s_l = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)  # 文本模态特定编码器
        self.encoder_s_v = nn.Conv1d(self.d_v, self.d_v, kernel_size=1, padding=0, bias=False)  # 视频模态特定编码器
        self.encoder_s_a = nn.Conv1d(self.d_a, self.d_a, kernel_size=1, padding=0, bias=False)  # 音频模态特定编码器

        # ===== 2.2 模态不变编码器 (Modality-Invariant Encoder) =====
        # 用于提取跨模态共享的特征，这些特征在不同模态间具有相似的语义
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)  # 共享编码器（所有模态共用）

        # ===== 3. 解码器：用于重构三种模态 =====
        # 通过模态特定特征和模态不变特征的组合来重构原始特征，用于训练时的重构损失
        self.decoder_l = nn.Conv1d(self.d_l * 2, self.d_l, kernel_size=1, padding=0, bias=False)  # 文本解码器
        self.decoder_v = nn.Conv1d(self.d_v * 2, self.d_v, kernel_size=1, padding=0, bias=False)  # 视频解码器
        self.decoder_a = nn.Conv1d(self.d_a * 2, self.d_a, kernel_size=1, padding=0, bias=False)  # 音频解码器

        # ===== 4. 余弦相似度计算投影层 =====
        # 用于计算模态特定特征之间的余弦相似度，确保不同模态的特定特征尽可能不相关
        self.proj_cosine_l = nn.Linear(combined_dim_low * (self.len_l - args.conv1d_kernel_size_l + 1), combined_dim_low)  # 文本余弦投影
        self.proj_cosine_v = nn.Linear(combined_dim_low * (self.len_v - args.conv1d_kernel_size_v + 1), combined_dim_low)  # 视频余弦投影
        self.proj_cosine_a = nn.Linear(combined_dim_low * (self.len_a - args.conv1d_kernel_size_a + 1), combined_dim_low)  # 音频余弦投影

        # ===== 5. 模态不变特征对齐层 =====
        # 将不同模态的模态不变特征对齐到相同的表示空间，便于计算相似度损失
        self.align_c_l = nn.Linear(combined_dim_low * (self.len_l - args.conv1d_kernel_size_l + 1), combined_dim_low)  # 文本模态不变特征对齐
        self.align_c_v = nn.Linear(combined_dim_low * (self.len_v - args.conv1d_kernel_size_v + 1), combined_dim_low)  # 视频模态不变特征对齐
        self.align_c_a = nn.Linear(combined_dim_low * (self.len_a - args.conv1d_kernel_size_a + 1), combined_dim_low)  # 音频模态不变特征对齐

        # ===== 6. 模态不变特征的自注意力机制 =====
        # 对每种模态的模态不变特征进行自注意力处理，增强特征表达能力
        self.self_attentions_c_l = self.get_network(self_type='l')  # 文本模态不变特征自注意力
        self.self_attentions_c_v = self.get_network(self_type='v')  # 视频模态不变特征自注意力
        self.self_attentions_c_a = self.get_network(self_type='a')  # 音频模态不变特征自注意力

        # ===== 7. 模态不变特征融合网络 =====
        # 将三种模态的模态不变特征融合后进行分类预测
        self.proj1_c = nn.Linear(self.d_l * 3, self.d_l * 3)  # 第一个投影层
        self.proj2_c = nn.Linear(self.d_l * 3, self.d_l * 3)  # 第二个投影层
        self.out_layer_c = nn.Linear(self.d_l * 3, output_dim)  # 输出层

        # ===== 8. 跨模态注意力机制 (Cross-modal Attentions) =====
        # 让每种模态的特征关注其他模态的信息，实现跨模态信息交互
        self.trans_l_with_a = self.get_network(self_type='la')  # 文本关注音频
        self.trans_l_with_v = self.get_network(self_type='lv')  # 文本关注视频
        self.trans_a_with_l = self.get_network(self_type='al')  # 音频关注文本
        self.trans_a_with_v = self.get_network(self_type='av')  # 音频关注视频
        self.trans_v_with_l = self.get_network(self_type='vl')  # 视频关注文本
        self.trans_v_with_a = self.get_network(self_type='va')  # 视频关注音频

        # ===== 9. 记忆网络 (Memory Networks) =====
        # 对跨模态注意力的结果进行进一步处理，增强记忆能力
        self.trans_l_mem = self.get_network(self_type='l_mem', layers=3)  # 文本记忆网络
        self.trans_a_mem = self.get_network(self_type='a_mem', layers=3)  # 音频记忆网络
        self.trans_v_mem = self.get_network(self_type='v_mem', layers=3)  # 视频记忆网络

        # ===== 10. 同构图蒸馏的全连接层 (Homogeneous Graph Distillation) =====
        # 处理模态不变特征，用于同构图中的知识蒸馏，每种模态单独处理
        # 文本模态的同构图蒸馏网络
        self.proj1_l_low = nn.Linear(combined_dim_low * (self.len_l - args.conv1d_kernel_size_l + 1), combined_dim_low)  # 文本第一投影层
        self.proj2_l_low = nn.Linear(combined_dim_low, combined_dim_low * (self.len_l - args.conv1d_kernel_size_l + 1))  # 文本第二投影层
        self.out_layer_l_low = nn.Linear(combined_dim_low * (self.len_l - args.conv1d_kernel_size_l + 1), output_dim)  # 文本输出层
        # 视频模态的同构图蒸馏网络
        self.proj1_v_low = nn.Linear(combined_dim_low * (self.len_v - args.conv1d_kernel_size_v + 1), combined_dim_low)  # 视频第一投影层
        self.proj2_v_low = nn.Linear(combined_dim_low, combined_dim_low * (self.len_v - args.conv1d_kernel_size_v + 1))  # 视频第二投影层
        self.out_layer_v_low = nn.Linear(combined_dim_low * (self.len_v - args.conv1d_kernel_size_v + 1), output_dim)  # 视频输出层
        # 音频模态的同构图蒸馏网络
        self.proj1_a_low = nn.Linear(combined_dim_low * (self.len_a - args.conv1d_kernel_size_a + 1), combined_dim_low)  # 音频第一投影层
        self.proj2_a_low = nn.Linear(combined_dim_low, combined_dim_low * (self.len_a - args.conv1d_kernel_size_a + 1))  # 音频第二投影层
        self.out_layer_a_low = nn.Linear(combined_dim_low * (self.len_a - args.conv1d_kernel_size_a + 1), output_dim)  # 音频输出层

        # ===== 11. 异构图蒸馏的全连接层 (Heterogeneous Graph Distillation) =====
        # 处理跨模态融合后的特征，用于异构图中的知识蒸馏
        # 文本模态的异构图蒸馏网络
        self.proj1_l_high = nn.Linear(combined_dim_high, combined_dim_high)  # 文本第一投影层
        self.proj2_l_high = nn.Linear(combined_dim_high, combined_dim_high)  # 文本第二投影层
        self.out_layer_l_high = nn.Linear(combined_dim_high, output_dim)  # 文本输出层
        # 视频模态的异构图蒸馏网络
        self.proj1_v_high = nn.Linear(combined_dim_high, combined_dim_high)  # 视频第一投影层
        self.proj2_v_high = nn.Linear(combined_dim_high, combined_dim_high)  # 视频第二投影层
        self.out_layer_v_high = nn.Linear(combined_dim_high, output_dim)  # 视频输出层
        # 音频模态的异构图蒸馏网络
        self.proj1_a_high = nn.Linear(combined_dim_high, combined_dim_high)  # 音频第一投影层
        self.proj2_a_high = nn.Linear(combined_dim_high, combined_dim_high)  # 音频第二投影层
        self.out_layer_a_high = nn.Linear(combined_dim_high, output_dim)  # 音频输出层

        # ===== 12. 集成投影层 (Ensemble Projection Layers) =====
        # 用于最终的多模态特征融合和预测

        # 每种模态的权重网络，用于学习模态重要性
        self.weight_l = nn.Linear(2 * self.d_l, 2 * self.d_l)  # 文本模态权重网络
        self.weight_v = nn.Linear(2 * self.d_v, 2 * self.d_v)  # 视频模态权重网络
        self.weight_a = nn.Linear(2 * self.d_a, 2 * self.d_a)  # 音频模态权重网络
        self.weight_c = nn.Linear(3 * self.d_l, 3 * self.d_l)  # 模态不变特征权重网络

        # 最终投影网络，将所有特征融合后进行最终预测
        self.proj1 = nn.Linear(combined_dim, combined_dim)  # 第一个投影层
        self.proj2 = nn.Linear(combined_dim, combined_dim)  # 第二个投影层
        self.out_layer = nn.Linear(combined_dim, output_dim)  # 最终输出层

    def get_network(self, self_type='l', layers=-1):
        """
        根据网络类型创建对应的Transformer编码器

        Args:
            self_type (str): 网络类型，决定了嵌入维度和dropout参数
                - 'l', 'al', 'vl': 文本相关网络
                - 'a', 'la', 'va': 音频相关网络
                - 'v', 'lv', 'av': 视频相关网络
                - 'l_mem', 'a_mem', 'v_mem': 记忆网络
            layers (int): Transformer层数，-1表示使用默认层数

        Returns:
            TransformerEncoder: 配置好的Transformer编码器
        """
        # 根据网络类型设置嵌入维度和注意力dropout
        if self_type in ['l', 'al', 'vl']:  # 文本相关网络
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        elif self_type in ['a', 'la', 'va']:  # 音频相关网络
            embed_dim, attn_dropout = self.d_a, self.attn_dropout_a
        elif self_type in ['v', 'lv', 'av']:  # 视频相关网络
            embed_dim, attn_dropout = self.d_v, self.attn_dropout_v
        elif self_type == 'l_mem':  # 文本记忆网络（维度翻倍）
            embed_dim, attn_dropout = 2 * self.d_l, self.attn_dropout
        elif self_type == 'a_mem':  # 音频记忆网络（维度翻倍）
            embed_dim, attn_dropout = 2 * self.d_a, self.attn_dropout
        elif self_type == 'v_mem':  # 视频记忆网络（维度翻倍）
            embed_dim, attn_dropout = 2 * self.d_v, self.attn_dropout
        else:
            raise ValueError("Unknown network type")

        # 创建并返回Transformer编码器
        return TransformerEncoder(embed_dim=embed_dim,
                                  num_heads=self.num_heads,
                                  layers=max(self.layers, layers),
                                  attn_dropout=attn_dropout,
                                  relu_dropout=self.relu_dropout,
                                  res_dropout=self.res_dropout,
                                  embed_dropout=self.embed_dropout,
                                  attn_mask=self.attn_mask)

    def forward(self, text, audio, video, is_distill=False):
        """
        DMD模型的前向传播过程

        Args:
            text: 文本特征 [batch_size, seq_len, text_dim]
            audio: 音频特征 [batch_size, seq_len, audio_dim]
            video: 视频特征 [batch_size, seq_len, video_dim]
            is_distill: 是否进行知识蒸馏（当前未使用）

        Returns:
            dict: 包含各种中间结果和最终预测的字典
        """

        # ===== 步骤1: 文本特征预处理 =====
        if self.use_bert:
            text = self.text_model(text)  # 使用BERT编码文本
        # 转置并应用dropout：[batch_size, text_dim, seq_len]
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training)
        # 音频和视频特征转置：[batch_size, feature_dim, seq_len]
        x_a = audio.transpose(1, 2)
        x_v = video.transpose(1, 2)

        # ===== 步骤2: 特征维度投影 =====
        # 如果原始维度与目标维度不同，则进行投影；否则直接使用原始特征
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)  # 文本特征投影
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)  # 音频特征投影
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)  # 视频特征投影

        # ===== 步骤3: 特征解耦 - 提取模态特定特征 =====
        s_l = self.encoder_s_l(proj_x_l)  # 文本模态特定特征
        s_v = self.encoder_s_v(proj_x_v)  # 视频模态特定特征
        s_a = self.encoder_s_a(proj_x_a)  # 音频模态特定特征

        # ===== 步骤4: 特征解耦 - 提取模态不变特征 =====
        c_l = self.encoder_c(proj_x_l)  # 文本的模态不变特征
        c_v = self.encoder_c(proj_x_v)  # 视频的模态不变特征
        c_a = self.encoder_c(proj_x_a)  # 音频的模态不变特征
        c_list = [c_l, c_v, c_a]  # 模态不变特征列表

        # ===== 步骤5: 模态不变特征对齐 =====
        # 将模态不变特征对齐到相同的表示空间，用于计算相似度损失
        c_l_sim = self.align_c_l(c_l.contiguous().view(x_l.size(0), -1))  # 文本模态不变特征对齐
        c_v_sim = self.align_c_v(c_v.contiguous().view(x_l.size(0), -1))  # 视频模态不变特征对齐
        c_a_sim = self.align_c_a(c_a.contiguous().view(x_l.size(0), -1))  # 音频模态不变特征对齐

        # ===== 步骤6: 特征重构 =====
        # 通过模态特定特征和模态不变特征的组合重构原始特征
        recon_l = self.decoder_l(torch.cat([s_l, c_list[0]], dim=1))  # 重构文本特征
        recon_v = self.decoder_v(torch.cat([s_v, c_list[1]], dim=1))  # 重构视频特征
        recon_a = self.decoder_a(torch.cat([s_a, c_list[2]], dim=1))  # 重构音频特征

        # ===== 步骤7: 重构特征的模态特定编码 =====
        # 对重构的特征再次进行模态特定编码，用于计算重构损失
        s_l_r = self.encoder_s_l(recon_l)  # 重构文本的模态特定特征
        s_v_r = self.encoder_s_v(recon_v)  # 重构视频的模态特定特征
        s_a_r = self.encoder_s_a(recon_a)  # 重构音频的模态特定特征

        # ===== 步骤8: 维度变换准备Transformer处理 =====
        # 将特征从 [batch_size, feature_dim, seq_len] 转换为 [seq_len, batch_size, feature_dim]
        # 这是Transformer期望的输入格式
        s_l = s_l.permute(2, 0, 1)  # 文本模态特定特征维度变换
        s_v = s_v.permute(2, 0, 1)  # 视频模态特定特征维度变换
        s_a = s_a.permute(2, 0, 1)  # 音频模态特定特征维度变换

        c_l = c_l.permute(2, 0, 1)  # 文本模态不变特征维度变换
        c_v = c_v.permute(2, 0, 1)  # 视频模态不变特征维度变换
        c_a = c_a.permute(2, 0, 1)  # 音频模态不变特征维度变换

        # ===== 步骤9: 同构图蒸馏 - 处理模态不变特征 =====
        # 文本模态的同构图蒸馏
        hs_l_low = c_l.transpose(0, 1).contiguous().view(x_l.size(0), -1)  # 展平文本模态不变特征
        repr_l_low = self.proj1_l_low(hs_l_low)  # 第一次投影
        hs_proj_l_low = self.proj2_l_low(
            F.dropout(F.relu(repr_l_low, inplace=True), p=self.output_dropout, training=self.training))  # 第二次投影
        hs_proj_l_low += hs_l_low  # 残差连接
        logits_l_low = self.out_layer_l_low(hs_proj_l_low)  # 输出预测

        # 视频模态的同构图蒸馏
        hs_v_low = c_v.transpose(0, 1).contiguous().view(x_v.size(0), -1)  # 展平视频模态不变特征
        repr_v_low = self.proj1_v_low(hs_v_low)  # 第一次投影
        hs_proj_v_low = self.proj2_v_low(
            F.dropout(F.relu(repr_v_low, inplace=True), p=self.output_dropout, training=self.training))  # 第二次投影
        hs_proj_v_low += hs_v_low  # 残差连接
        logits_v_low = self.out_layer_v_low(hs_proj_v_low)  # 输出预测

        # 音频模态的同构图蒸馏
        hs_a_low = c_a.transpose(0, 1).contiguous().view(x_a.size(0), -1)  # 展平音频模态不变特征
        repr_a_low = self.proj1_a_low(hs_a_low)  # 第一次投影
        hs_proj_a_low = self.proj2_a_low(
            F.dropout(F.relu(repr_a_low, inplace=True), p=self.output_dropout, training=self.training))  # 第二次投影
        hs_proj_a_low += hs_a_low  # 残差连接
        logits_a_low = self.out_layer_a_low(hs_proj_a_low)  # 输出预测

        # ===== 步骤10: 模态特定特征投影（用于余弦相似度计算） =====
        proj_s_l = self.proj_cosine_l(s_l.transpose(0, 1).contiguous().view(x_l.size(0), -1))  # 文本模态特定特征投影
        proj_s_v = self.proj_cosine_v(s_v.transpose(0, 1).contiguous().view(x_l.size(0), -1))  # 视频模态特定特征投影
        proj_s_a = self.proj_cosine_a(s_a.transpose(0, 1).contiguous().view(x_l.size(0), -1))  # 音频模态特定特征投影

        # ===== 步骤11: 模态不变特征的自注意力处理 =====
        # 对每种模态的模态不变特征应用自注意力机制
        c_l_att = self.self_attentions_c_l(c_l)  # 文本模态不变特征自注意力
        if type(c_l_att) == tuple:  # 处理可能的元组返回值
            c_l_att = c_l_att[0]
        c_l_att = c_l_att[-1]  # 取最后一层的输出

        c_v_att = self.self_attentions_c_v(c_v)  # 视频模态不变特征自注意力
        if type(c_v_att) == tuple:  # 处理可能的元组返回值
            c_v_att = c_v_att[0]
        c_v_att = c_v_att[-1]  # 取最后一层的输出

        c_a_att = self.self_attentions_c_a(c_a)  # 音频模态不变特征自注意力
        if type(c_a_att) == tuple:  # 处理可能的元组返回值
            c_a_att = c_a_att[0]
        c_a_att = c_a_att[-1]  # 取最后一层的输出

        # ===== 步骤12: 模态不变特征融合 =====
        c_fusion = torch.cat([c_l_att, c_v_att, c_a_att], dim=1)  # 拼接三种模态的模态不变特征

        # 通过两层全连接网络处理融合后的特征
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(c_fusion), inplace=True), p=self.output_dropout,
                      training=self.training))  # 第二层投影
        c_proj += c_fusion  # 残差连接
        logits_c = self.out_layer_c(c_proj)  # 模态不变特征的预测输出

        # ===== 步骤13: 跨模态注意力机制 =====
        # 让每种模态关注其他模态的信息，实现跨模态信息交互

        # 文本模态关注音频和视频模态 (V,A) --> L
        h_l_with_as = self.trans_l_with_a(s_l, s_a, s_a)  # 文本关注音频，维度 (L, N, d_l)
        h_l_with_vs = self.trans_l_with_v(s_l, s_v, s_v)  # 文本关注视频，维度 (L, N, d_l)
        h_ls = torch.cat([h_l_with_as, h_l_with_vs], dim=2)  # 拼接两个注意力结果
        h_ls = self.trans_l_mem(h_ls)  # 通过记忆网络进一步处理
        if type(h_ls) == tuple:  # 处理可能的元组返回值
            h_ls = h_ls[0]
        last_h_l = last_hs = h_ls[-1]  # 取最后一个时间步的输出用于预测

        # 音频模态关注文本和视频模态 (L,V) --> A
        h_a_with_ls = self.trans_a_with_l(s_a, s_l, s_l)  # 音频关注文本
        h_a_with_vs = self.trans_a_with_v(s_a, s_v, s_v)  # 音频关注视频
        h_as = torch.cat([h_a_with_ls, h_a_with_vs], dim=2)  # 拼接两个注意力结果
        h_as = self.trans_a_mem(h_as)  # 通过记忆网络进一步处理
        if type(h_as) == tuple:  # 处理可能的元组返回值
            h_as = h_as[0]
        last_h_a = last_hs = h_as[-1]  # 取最后一个时间步的输出

        # 视频模态关注文本和音频模态 (L,A) --> V
        h_v_with_ls = self.trans_v_with_l(s_v, s_l, s_l)  # 视频关注文本
        h_v_with_as = self.trans_v_with_a(s_v, s_a, s_a)  # 视频关注音频
        h_vs = torch.cat([h_v_with_ls, h_v_with_as], dim=2)  # 拼接两个注意力结果
        h_vs = self.trans_v_mem(h_vs)  # 通过记忆网络进一步处理
        if type(h_vs) == tuple:  # 处理可能的元组返回值
            h_vs = h_vs[0]
        last_h_v = last_hs = h_vs[-1]  # 取最后一个时间步的输出

        # ===== 步骤14: 异构图蒸馏 - 处理跨模态融合特征 =====
        # 文本模态的异构图蒸馏
        hs_proj_l_high = self.proj2_l_high(
            F.dropout(F.relu(self.proj1_l_high(last_h_l), inplace=True), p=self.output_dropout, training=self.training))  # 第二层投影
        hs_proj_l_high += last_h_l  # 残差连接
        logits_l_high = self.out_layer_l_high(hs_proj_l_high)  # 文本异构图预测

        # 视频模态的异构图蒸馏
        hs_proj_v_high = self.proj2_v_high(
            F.dropout(F.relu(self.proj1_v_high(last_h_v), inplace=True), p=self.output_dropout, training=self.training))  # 第二层投影
        hs_proj_v_high += last_h_v  # 残差连接
        logits_v_high = self.out_layer_v_high(hs_proj_v_high)  # 视频异构图预测

        # 音频模态的异构图蒸馏
        hs_proj_a_high = self.proj2_a_high(
            F.dropout(F.relu(self.proj1_a_high(last_h_a), inplace=True), p=self.output_dropout,
                      training=self.training))  # 第二层投影
        hs_proj_a_high += last_h_a  # 残差连接
        logits_a_high = self.out_layer_a_high(hs_proj_a_high)  # 音频异构图预测

        # ===== 步骤15: 特征加权和最终融合 =====
        # 通过权重网络学习每种模态的重要性
        last_h_l = torch.sigmoid(self.weight_l(last_h_l))  # 文本模态权重
        last_h_v = torch.sigmoid(self.weight_v(last_h_v))  # 视频模态权重
        last_h_a = torch.sigmoid(self.weight_a(last_h_a))  # 音频模态权重
        c_fusion = torch.sigmoid(self.weight_c(c_fusion))  # 模态不变特征权重

        # ===== 步骤16: 最终特征融合和预测 =====
        # 将所有加权后的特征拼接
        last_hs = torch.cat([last_h_l, last_h_v, last_h_a, c_fusion], dim=1)
        # 通过最终的投影网络
        last_hs_proj = self.proj2(
            F.dropout(F.relu(self.proj1(last_hs), inplace=True), p=self.output_dropout, training=self.training))
        last_hs_proj += last_hs  # 残差连接

        # 最终预测输出
        output = self.out_layer(last_hs_proj)

        # ===== 步骤17: 返回结果字典 =====
        # 包含所有中间结果和最终预测，用于损失计算和分析
        res = {
            # 同构图蒸馏结果（基于模态不变特征）
            'logits_l_homo': logits_l_low,      # 文本同构图预测
            'logits_v_homo': logits_v_low,      # 视频同构图预测
            'logits_a_homo': logits_a_low,      # 音频同构图预测
            'repr_l_homo': repr_l_low,          # 文本同构图表示
            'repr_v_homo': repr_v_low,          # 视频同构图表示
            'repr_a_homo': repr_a_low,          # 音频同构图表示

            # 原始投影特征
            'origin_l': proj_x_l,               # 投影后的文本特征
            'origin_v': proj_x_v,               # 投影后的视频特征
            'origin_a': proj_x_a,               # 投影后的音频特征

            # 模态特定特征
            's_l': s_l,                         # 文本模态特定特征
            's_v': s_v,                         # 视频模态特定特征
            's_a': s_a,                         # 音频模态特定特征
            'proj_s_l': proj_s_l,               # 投影后的文本模态特定特征
            'proj_s_v': proj_s_v,               # 投影后的视频模态特定特征
            'proj_s_a': proj_s_a,               # 投影后的音频模态特定特征

            # 模态不变特征
            'c_l': c_l,                         # 文本模态不变特征
            'c_v': c_v,                         # 视频模态不变特征
            'c_a': c_a,                         # 音频模态不变特征
            'c_l_sim': c_l_sim,                 # 对齐后的文本模态不变特征
            'c_v_sim': c_v_sim,                 # 对齐后的视频模态不变特征
            'c_a_sim': c_a_sim,                 # 对齐后的音频模态不变特征

            # 重构相关
            's_l_r': s_l_r,                     # 重构文本的模态特定特征
            's_v_r': s_v_r,                     # 重构视频的模态特定特征
            's_a_r': s_a_r,                     # 重构音频的模态特定特征
            'recon_l': recon_l,                 # 重构的文本特征
            'recon_v': recon_v,                 # 重构的视频特征
            'recon_a': recon_a,                 # 重构的音频特征

            # 异构图蒸馏结果（基于跨模态融合特征）
            'logits_l_hetero': logits_l_high,   # 文本异构图预测
            'logits_v_hetero': logits_v_high,   # 视频异构图预测
            'logits_a_hetero': logits_a_high,   # 音频异构图预测
            'repr_l_hetero': hs_proj_l_high,    # 文本异构图表示
            'repr_v_hetero': hs_proj_v_high,    # 视频异构图表示
            'repr_a_hetero': hs_proj_a_high,    # 音频异构图表示

            # 跨模态注意力结果
            'last_h_l': h_ls[-1],               # 文本跨模态注意力最终输出
            'last_h_v': h_vs[-1],               # 视频跨模态注意力最终输出
            'last_h_a': h_as[-1],               # 音频跨模态注意力最终输出

            # 最终预测结果
            'logits_c': logits_c,               # 模态不变特征预测
            'output_logit': output              # 最终融合预测（主要输出）
        }
        return res